import React from 'react';
import { Loader2 } from 'lucide-react';

const Button = ({
    children,
    onClick,
    variant = 'primary',
    size = 'md',
    disabled = false,
    loading = false,
    icon: Icon,
    iconPosition = 'left',
    fullWidth = false,
    className = '',
    ...props
}) => {
    const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

    const variants = {
        primary: "bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500 shadow-md hover:shadow-lg",
        secondary: "bg-gray-100 hover:bg-gray-200 text-gray-900 focus:ring-gray-500 border border-gray-300",
        success: "bg-green-600 hover:bg-green-700 text-white focus:ring-green-500 shadow-md hover:shadow-lg",
        danger: "bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 shadow-md hover:shadow-lg",
        warning: "bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500 shadow-md hover:shadow-lg",
        outline: "border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500",
        ghost: "text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500",
        link: "text-blue-600 hover:text-blue-700 underline focus:ring-blue-500 p-0"
    };

    const sizes = {
        xs: "px-2 py-1 text-xs",
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-sm",
        lg: "px-6 py-3 text-base",
        xl: "px-8 py-4 text-lg"
    };

    const isDisabled = disabled || loading;

    const buttonClasses = `
    ${baseClasses}
    ${variants[variant]}
    ${sizes[size]}
    ${fullWidth ? 'w-full' : ''}
    ${isDisabled ? 'transform-none' : 'hover:scale-105 active:scale-95'}
    ${className}
  `.trim();

    const iconClasses = "flex-shrink-0";
    const iconSizes = {
        xs: "w-3 h-3",
        sm: "w-4 h-4",
        md: "w-4 h-4",
        lg: "w-5 h-5",
        xl: "w-6 h-6"
    };

    const renderIcon = () => {
        if (loading) {
            return <Loader2 className={`${iconClasses} ${iconSizes[size]} animate-spin`} />;
        }
        if (Icon) {
            return <Icon className={`${iconClasses} ${iconSizes[size]}`} />;
        }
        return null;
    };

    const renderContent = () => {
        const icon = renderIcon();
        const hasIcon = icon !== null;
        const hasText = children !== null && children !== undefined;

        if (!hasIcon && !hasText) return null;

        if (!hasText) return icon;

        if (!hasIcon) return children;

        // Both icon and text
        const spacing = size === 'xs' ? 'space-x-1' : 'space-x-2';

        if (iconPosition === 'right') {
            return (
                <span className={`flex items-center ${spacing}`}>
                    <span>{children}</span>
                    {icon}
                </span>
            );
        }

        return (
            <span className={`flex items-center ${spacing}`}>
                {icon}
                <span>{children}</span>
            </span>
        );
    };

    return (
        <button
            onClick={onClick}
            disabled={isDisabled}
            className={buttonClasses}
            {...props}
        >
            {renderContent()}
        </button>
    );
};

export default Button;
