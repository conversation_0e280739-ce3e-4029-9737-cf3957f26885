import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Send, AlertTriangle, Eye, EyeOff, Shield, Wifi, WifiOff } from 'lucide-react';
import OptionsList from '../components/Test/OptionsList';
import Timer from '../components/Test/Timer';
import QuestionCard from '../components/Test/QuestionCard';
import QuestionNavigation from '../components/Test/QuestionNavigation';
import Button from '../components/UI/Button';
import Modal, { ConfirmModal } from '../components/UI/Modal';

// Dummy test data
const dummyTestData = {
    id: 'test_001',
    title: 'Software Developer Aptitude Test',
    description: 'A comprehensive test covering programming concepts, logical reasoning, and problem-solving skills.',
    duration: 3600, // 60 minutes in seconds
    totalQuestions: 15,
    passingScore: 70,
    questions: [
        {
            id: 'q1',
            title: 'What is the time complexity of binary search?',
            description: 'Consider a sorted array of n elements.',
            type: 'single',
            difficulty: 'medium',
            category: 'Algorithms',
            points: 2,
            timeLimit: 120,
            options: [
                { id: 'a', label: 'A', text: 'O(n)' },
                { id: 'b', label: 'B', text: 'O(log n)' },
                { id: 'c', label: 'C', text: 'O(n²)' },
                { id: 'd', label: 'D', text: 'O(n log n)' }
            ],
            correctAnswer: 'b'
        },
        {
            id: 'q2',
            title: 'Which of the following are valid JavaScript data types?',
            description: 'Select all that apply.',
            type: 'multiple',
            difficulty: 'easy',
            category: 'JavaScript',
            points: 3,
            options: [
                { id: 'a', label: 'A', text: 'string' },
                { id: 'b', label: 'B', text: 'integer' },
                { id: 'c', label: 'C', text: 'boolean' },
                { id: 'd', label: 'D', text: 'object' },
                { id: 'e', label: 'E', text: 'float' }
            ],
            correctAnswer: ['a', 'c', 'd']
        },
        {
            id: 'q3',
            title: 'What will be the output of this code?',
            code: `function factorial(n) {
  if (n <= 1) return 1;
  return n * factorial(n - 1);
}
console.log(factorial(5));`,
            type: 'single',
            difficulty: 'medium',
            category: 'Programming Logic',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: '120' },
                { id: 'b', label: 'B', text: '24' },
                { id: 'c', label: 'C', text: '5' },
                { id: 'd', label: 'D', text: 'Error' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q4',
            title: 'Which sorting algorithm has the best average time complexity?',
            type: 'single',
            difficulty: 'hard',
            category: 'Data Structures',
            points: 3,
            options: [
                { id: 'a', label: 'A', text: 'Bubble Sort - O(n²)' },
                { id: 'b', label: 'B', text: 'Quick Sort - O(n log n)' },
                { id: 'c', label: 'C', text: 'Selection Sort - O(n²)' },
                { id: 'd', label: 'D', text: 'Insertion Sort - O(n²)' }
            ],
            correctAnswer: 'b'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },

        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        },
        {
            id: 'q5',
            title: 'In object-oriented programming, what does encapsulation mean?',
            type: 'single',
            difficulty: 'medium',
            category: 'OOP Concepts',
            points: 2,
            options: [
                { id: 'a', label: 'A', text: 'Hiding internal implementation details' },
                { id: 'b', label: 'B', text: 'Creating multiple instances of a class' },
                { id: 'c', label: 'C', text: 'Inheriting properties from parent class' },
                { id: 'd', label: 'D', text: 'Overriding methods in derived classes' }
            ],
            correctAnswer: 'a'
        }

    ]
};

const TestPage = () => {
    // Test state
    const [testData] = useState(dummyTestData);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [answers, setAnswers] = useState({});
    const [timeLeft, setTimeLeft] = useState(dummyTestData.duration);
    const [isTestActive, setIsTestActive] = useState(true);
    const [isTestCompleted, setIsTestCompleted] = useState(false);
    const [questionTimeSpent, setQuestionTimeSpent] = useState({});

    // Anti-cheat state
    const [tabSwitchCount, setTabSwitchCount] = useState(0);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [devToolsWarning, setDevToolsWarning] = useState(false);

    // UI state
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [showTimeUpModal, setShowTimeUpModal] = useState(false);
    const [showWarningModal, setShowWarningModal] = useState(false);
    const [warningMessage, setWarningMessage] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const currentQuestion = testData.questions[currentQuestionIndex];
    const selectedAnswer = answers[currentQuestion.id] || (currentQuestion.type === 'multiple' ? [] : null);

    // Anti-cheat measures
    useEffect(() => {
        // Request fullscreen
        const requestFullscreen = () => {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        };

        // Tab switch detection
        const handleVisibilityChange = () => {
            if (document.hidden && isTestActive) {
                setTabSwitchCount(prev => prev + 1);
                setWarningMessage(`Warning: Tab switching detected! Count: ${tabSwitchCount + 1}/3`);
                setShowWarningModal(true);

                if (tabSwitchCount >= 2) {
                    handleAutoSubmit('Multiple tab switches detected');
                }
            }
        };

        // Fullscreen change detection
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
            if (!document.fullscreenElement && isTestActive) {
                setWarningMessage('Please return to fullscreen mode to continue the test.');
                setShowWarningModal(true);
            }
        };

        // Network status
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        // Dev tools detection (basic)
        const detectDevTools = () => {
            const threshold = 160;
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {
                if (!devToolsWarning) {
                    setDevToolsWarning(true);
                    setWarningMessage('Developer tools detected! Please close dev tools to continue.');
                    setShowWarningModal(true);
                }
            }
        };

        // Prevent right-click and key combinations
        const preventCheating = (e) => {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                setWarningMessage('This action is not allowed during the test.');
                setShowWarningModal(true);
            }
        };

        const preventRightClick = (e) => {
            e.preventDefault();
            setWarningMessage('Right-click is disabled during the test.');
            setShowWarningModal(true);
        };

        if (isTestActive) {
            requestFullscreen();
            document.addEventListener('visibilitychange', handleVisibilityChange);
            document.addEventListener('fullscreenchange', handleFullscreenChange);
            document.addEventListener('keydown', preventCheating);
            document.addEventListener('contextmenu', preventRightClick);
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);

            const devToolsInterval = setInterval(detectDevTools, 1000);

            return () => {
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                document.removeEventListener('fullscreenchange', handleFullscreenChange);
                document.removeEventListener('keydown', preventCheating);
                document.removeEventListener('contextmenu', preventRightClick);
                window.removeEventListener('online', handleOnline);
                window.removeEventListener('offline', handleOffline);
                clearInterval(devToolsInterval);
            };
        }
    }, [isTestActive, tabSwitchCount, devToolsWarning]);

    // Question timer
    useEffect(() => {
        const questionId = currentQuestion.id;
        const startTime = Date.now();

        return () => {
            const timeSpent = Math.floor((Date.now() - startTime) / 1000);
            setQuestionTimeSpent(prev => ({
                ...prev,
                [questionId]: (prev[questionId] || 0) + timeSpent
            }));
        };
    }, [currentQuestionIndex, currentQuestion.id]);

    // Auto-save answers
    useEffect(() => {
        const saveInterval = setInterval(() => {
            if (isTestActive) {
                // Auto-save to server would go here
                console.log('Auto-saving answers:', answers);
            }
        }, 30000); // Save every 30 seconds

        return () => clearInterval(saveInterval);
    }, [answers, isTestActive]);

    const handleAnswerSelect = (answer) => {
        setAnswers(prev => ({
            ...prev,
            [currentQuestion.id]: answer
        }));
    };

    const handleNext = () => {
        if (currentQuestionIndex < testData.questions.length - 1) {
            setCurrentQuestionIndex(prev => prev + 1);
        }
    };

    const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(prev => prev - 1);
        }
    };

    const handleTimeUp = () => {
        setIsTestActive(false);
        setShowTimeUpModal(true);
    };

    const handleAutoSubmit = (reason) => {
        setIsTestActive(false);
        setIsTestCompleted(true);
        console.log(`Test auto-submitted: ${reason}`);
        // Submit to server
    };

    const handleSubmitTest = async () => {
        setIsSubmitting(true);

        try {
            // Calculate results
            let score = 0;
            let totalPoints = 0;

            testData.questions.forEach(question => {
                totalPoints += question.points;
                const userAnswer = answers[question.id];

                if (question.type === 'single' && userAnswer === question.correctAnswer) {
                    score += question.points;
                } else if (question.type === 'multiple' && userAnswer) {
                    const correct = question.correctAnswer.every(ans => userAnswer.includes(ans)) &&
                        userAnswer.every(ans => question.correctAnswer.includes(ans));
                    if (correct) score += question.points;
                }
            });

            const percentage = Math.round((score / totalPoints) * 100);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            const testResult = {
                testId: testData.id,
                score,
                totalPoints,
                percentage,
                passed: percentage >= testData.passingScore,
                answers,
                timeSpent: testData.duration - timeLeft,
                questionTimeSpent,
                tabSwitchCount,
                submittedAt: new Date().toISOString()
            };

            console.log('Test submitted:', testResult);
            setIsTestCompleted(true);
            setIsTestActive(false);

        } catch (error) {
            console.error('Submission failed:', error);
            setWarningMessage('Failed to submit test. Please try again.');
            setShowWarningModal(true);
        } finally {
            setIsSubmitting(false);
            setShowSubmitModal(false);
        }
    };

    const getAnsweredCount = () => {
        return Object.keys(answers).length;
    };

    const isAnswered = (questionId) => {
        const answer = answers[questionId];
        if (Array.isArray(answer)) {
            return answer.length > 0;
        }
        return answer !== null && answer !== undefined && answer !== '';
    };

    if (isTestCompleted) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
                    <div className="mb-6">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Shield className="w-8 h-8 text-green-600" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Test Completed!</h1>
                        <p className="text-gray-600">Your responses have been submitted successfully.</p>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-gray-500">Questions:</span>
                                <div className="font-semibold">{getAnsweredCount()}/{testData.totalQuestions}</div>
                            </div>
                            <div>
                                <span className="text-gray-500">Time Used:</span>
                                <div className="font-semibold">
                                    {Math.floor((testData.duration - timeLeft) / 60)}m {(testData.duration - timeLeft) % 60}s
                                </div>
                            </div>
                        </div>
                    </div>

                    <p className="text-sm text-gray-500">
                        Results will be available shortly. You may now close this window.
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            {/* Header */}
            <div className="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-40">
                <div className="max-w-7xl mx-auto px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <h1 className="text-xl font-bold text-gray-900">{testData.title}</h1>
                            <div className="flex items-center space-x-2">
                                {!isOnline && <WifiOff className="w-5 h-5 text-red-500" />}
                                {isOnline && <Wifi className="w-5 h-5 text-green-500" />}
                                {!isFullscreen && <Eye className="w-5 h-5 text-orange-500" />}
                                {isFullscreen && <EyeOff className="w-5 h-5 text-green-500" />}
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            <div className="text-sm text-gray-600">
                                {getAnsweredCount()}/{testData.totalQuestions} answered
                            </div>
                            <Timer
                                initialTime={timeLeft}
                                onTimeUp={handleTimeUp}
                                isActive={isTestActive}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Question Navigation Sidebar */}
            <QuestionNavigation
                questions={testData.questions}
                currentQuestionIndex={currentQuestionIndex}
                answers={answers}
                onQuestionSelect={setCurrentQuestionIndex}
                isAnswered={isAnswered}
            />

            {/* Mobile Question Navigation */}
            <div className="lg:hidden bg-white shadow-sm border-b border-slate-200 sticky top-14 z-20">
                <div className="max-w-4xl mx-auto p-3">
                    <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-slate-900 text-sm">Questions</h3>
                        <span className="text-xs text-slate-600">
                            {Object.keys(answers).length}/{testData.questions.length}
                        </span>
                    </div>
                    <div className="grid grid-cols-10 gap-1.5">
                        {testData.questions.map((question, index) => (
                            <button
                                key={question.id}
                                onClick={() => setCurrentQuestionIndex(index)}
                                className={`
                                    w-7 h-7 rounded-md text-xs font-medium transition-all duration-200
                                    ${index === currentQuestionIndex
                                        ? 'bg-indigo-600 text-white shadow-md'
                                        : isAnswered(question.id)
                                            ? 'bg-emerald-100 text-emerald-800 border border-emerald-300'
                                            : 'bg-slate-100 text-slate-600 border border-slate-200'
                                    }
                                `}
                            >
                                {index + 1}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-4xl mx-auto p-4 lg:ml-80">
                <div className="space-y-4">
                        <QuestionCard
                            question={currentQuestion}
                            currentIndex={currentQuestionIndex}
                            totalQuestions={testData.totalQuestions}
                            timeSpent={questionTimeSpent[currentQuestion.id] || 0}
                            isAnswered={isAnswered(currentQuestion.id)}
                            difficulty={currentQuestion.difficulty}
                        />

                        {/* Options */}
                        <div className="bg-white rounded-lg shadow-md border border-slate-200 p-4">
                            <OptionsList
                                options={currentQuestion.options}
                                selectedOption={selectedAnswer}
                                onOptionSelect={handleAnswerSelect}
                                questionType={currentQuestion.type}
                                disabled={!isTestActive}
                            />
                        </div>

                        {/* Navigation */}
                        <div className="flex items-center justify-between bg-white rounded-lg shadow-md border border-slate-200 p-4">
                            <Button
                                variant="secondary"
                                onClick={handlePrevious}
                                disabled={currentQuestionIndex === 0}
                                icon={ChevronLeft}
                            >
                                Previous
                            </Button>

                            <div className="flex items-center space-x-4">
                                {currentQuestionIndex === testData.questions.length - 1 ? (
                                    <Button
                                        variant="success"
                                        onClick={() => setShowSubmitModal(true)}
                                        icon={Send}
                                        size="lg"
                                    >
                                        Submit Test
                                    </Button>
                                ) : (
                                    <Button
                                        variant="primary"
                                        onClick={handleNext}
                                        icon={ChevronRight}
                                        iconPosition="right"
                                    >
                                        Next
                                    </Button>
                                )}
                            </div>
                        </div>
                </div>
            </div>

            {/* Modals */}
            <ConfirmModal
                isOpen={showSubmitModal}
                onClose={() => setShowSubmitModal(false)}
                onConfirm={handleSubmitTest}
                title="Submit Test"
                message={`Are you sure you want to submit your test? You have answered ${getAnsweredCount()} out of ${testData.totalQuestions} questions.`}
                confirmText={isSubmitting ? "Submitting..." : "Submit"}
                type="info"
            />

            <Modal
                isOpen={showTimeUpModal}
                onClose={() => { }}
                title="Time's Up!"
                type="warning"
                showCloseButton={false}
                closeOnOverlayClick={false}
                closeOnEscape={false}
                footer={
                    <Button
                        variant="primary"
                        onClick={handleSubmitTest}
                        loading={isSubmitting}
                    >
                        Submit Test
                    </Button>
                }
            >
                <p className="text-gray-600 mb-4">
                    The test time has expired. Your test will be submitted automatically.
                </p>
                <div className="bg-orange-50 p-4 rounded-lg">
                    <p className="text-sm text-orange-800">
                        Answered: {getAnsweredCount()}/{testData.totalQuestions} questions
                    </p>
                </div>
            </Modal>

            <Modal
                isOpen={showWarningModal}
                onClose={() => setShowWarningModal(false)}
                title="Warning"
                type="warning"
                footer={
                    <Button variant="primary" onClick={() => setShowWarningModal(false)}>
                        I Understand
                    </Button>
                }
            >
                <p className="text-gray-600">{warningMessage}</p>
                {tabSwitchCount > 0 && (
                    <div className="mt-4 bg-red-50 p-4 rounded-lg">
                        <p className="text-sm text-red-800">
                            Tab switches: {tabSwitchCount}/3 (Test will auto-submit after 3 violations)
                        </p>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default TestPage;