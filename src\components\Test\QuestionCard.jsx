import React from 'react';
import { CheckCircle, Clock } from 'lucide-react';

const QuestionCard = ({
    question,
    currentIndex,
    totalQuestions,
    timeSpent = 0,
    isAnswered = false,
    difficulty = 'medium'
}) => {
    const getDifficultyColor = () => {
        switch (difficulty) {
            case 'easy': return 'bg-emerald-100 text-emerald-700 border-emerald-200';
            case 'medium': return 'bg-amber-100 text-amber-700 border-amber-200';
            case 'hard': return 'bg-red-100 text-red-700 border-red-200';
            default: return 'bg-slate-100 text-slate-700 border-slate-200';
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="bg-white rounded-lg shadow-md border border-slate-200 overflow-hidden">
            {/* Compact Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white px-4 py-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                        <span className="text-indigo-100 text-sm font-medium">
                            Q{currentIndex + 1}/{totalQuestions}
                        </span>
                        <span className={`px-2 py-0.5 rounded-md text-xs font-medium border ${getDifficultyColor()}`}>
                            {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                        </span>
                        <span className="text-xs text-indigo-200">
                            {question.category || 'General'}
                        </span>
                    </div>
                    <div className="flex items-center space-x-3">
                        {isAnswered && (
                            <CheckCircle className="w-4 h-4 text-emerald-300" />
                        )}
                        <div className="flex items-center space-x-1 text-indigo-200">
                            <Clock className="w-3 h-3" />
                            <span className="text-xs">{formatTime(timeSpent)}</span>
                        </div>
                    </div>
                </div>

                {/* Compact Progress Bar */}
                <div className="w-full bg-indigo-400 rounded-full h-1 mt-2">
                    <div
                        className="bg-white h-1 rounded-full transition-all duration-300"
                        style={{ width: `${((currentIndex + 1) / totalQuestions) * 100}%` }}
                    ></div>
                </div>
            </div>

            {/* Flexible Question Content */}
            <div className="px-4 py-3">
                <div className="mb-3">
                    <h2 className="text-lg font-semibold text-slate-900 leading-tight mb-2">
                        {question.title}
                    </h2>

                    {question.description && (
                        <div className="text-slate-600 text-sm leading-relaxed mb-3">
                            {question.description}
                        </div>
                    )}

                    {question.code && (
                        <div className="bg-slate-50 border border-slate-200 rounded-md p-3 mb-3 max-h-64 overflow-y-auto">
                            <pre className="text-xs text-slate-800 font-mono overflow-x-auto whitespace-pre-wrap">
                                <code>{question.code}</code>
                            </pre>
                        </div>
                    )}

                    {question.image && (
                        <div className="mb-3">
                            <img
                                src={question.image}
                                alt="Question illustration"
                                className="max-w-full h-auto rounded-md border border-slate-200 max-h-80 object-contain"
                            />
                        </div>
                    )}
                </div>

                {/* Compact Question Meta */}
                <div className="flex items-center justify-between text-xs text-slate-500 bg-slate-50 px-3 py-2 rounded-md">
                    <div className="flex items-center space-x-3">
                        <span className="font-medium">Points: {question.points || 1}</span>
                        {question.type === 'multiple' && (
                            <span className="text-amber-600 font-medium">Multiple Choice</span>
                        )}
                    </div>
                    <span className="text-slate-400">
                        {question.timeLimit ? `${Math.floor(question.timeLimit / 60)}:${(question.timeLimit % 60).toString().padStart(2, '0')} limit` : 'No limit'}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default QuestionCard;