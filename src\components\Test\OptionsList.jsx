import React from 'react';

const OptionsList = ({
    options,
    selectedOption,
    onOptionSelect,
    questionType = 'single',
    disabled = false
}) => {
    const handleOptionClick = (optionId) => {
        if (disabled) return;

        if (questionType === 'single') {
            onOptionSelect(optionId);
        } else if (questionType === 'multiple') {
            const newSelection = selectedOption.includes(optionId)
                ? selectedOption.filter(id => id !== optionId)
                : [...selectedOption, optionId];
            onOptionSelect(newSelection);
        }
    };

    return (
        <div className="space-y-2">
            {options.map((option) => {
                const isSelected = questionType === 'single'
                    ? selectedOption === option.id
                    : selectedOption.includes(option.id);

                return (
                    <div
                        key={option.id}
                        onClick={() => handleOptionClick(option.id)}
                        className={`
              p-3 rounded-lg border-2 cursor-pointer transition-all duration-200
              ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:shadow-sm hover:border-indigo-300'}
              ${isSelected
                                ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
                                : 'border-slate-200 bg-white hover:bg-slate-50'
                            }
            `}
                    >
                        <div className="flex items-start space-x-3">
                            <div className={`
                w-4 h-4 rounded-full border-2 flex items-center justify-center mt-0.5 flex-shrink-0
                ${isSelected ? 'border-indigo-500 bg-indigo-500' : 'border-slate-300'}
              `}>
                                {isSelected && (
                                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                                )}
                            </div>
                            <div className="flex-1 min-w-0">
                                <span className="text-slate-800 font-medium text-sm block mb-1">
                                    {option.label}
                                </span>
                                <div className="text-slate-600 text-sm leading-relaxed">
                                    {option.text}
                                </div>
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default OptionsList;