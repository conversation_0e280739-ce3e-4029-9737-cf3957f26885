import React from 'react';
import {
    Clock,
    AlertTriangle,
    CheckCircle,
    Info,
    Eye,
    Shield,
    BookOpen,
    Target,
    Users,
    Award
} from 'lucide-react';

const InstructionCard = ({
    type = 'info',
    title,
    description,
    items = [],
    icon: CustomIcon,
    highlight = false,
    className = ''
}) => {
    const typeConfig = {
        info: {
            icon: Info,
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200',
            iconColor: 'text-blue-600',
            titleColor: 'text-blue-900',
            textColor: 'text-blue-800'
        },
        warning: {
            icon: AlertTriangle,
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200',
            iconColor: 'text-yellow-600',
            titleColor: 'text-yellow-900',
            textColor: 'text-yellow-800'
        },
        success: {
            icon: CheckCircle,
            bgColor: 'bg-green-50',
            borderColor: 'border-green-200',
            iconColor: 'text-green-600',
            titleColor: 'text-green-900',
            textColor: 'text-green-800'
        },
        danger: {
            icon: Alert<PERSON>riangle,
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            iconColor: 'text-red-600',
            titleColor: 'text-red-900',
            textColor: 'text-red-800'
        },
        time: {
            icon: Clock,
            bgColor: 'bg-purple-50',
            borderColor: 'border-purple-200',
            iconColor: 'text-purple-600',
            titleColor: 'text-purple-900',
            textColor: 'text-purple-800'
        },
        security: {
            icon: Shield,
            bgColor: 'bg-indigo-50',
            borderColor: 'border-indigo-200',
            iconColor: 'text-indigo-600',
            titleColor: 'text-indigo-900',
            textColor: 'text-indigo-800'
        }
    };

    const config = typeConfig[type] || typeConfig.info;
    const IconComponent = CustomIcon || config.icon;

    return (
        <div className={`
      ${config.bgColor} ${config.borderColor} 
      ${highlight ? 'ring-2 ring-offset-2 ring-blue-500 shadow-lg' : 'shadow-md'}
      border-2 rounded-xl p-6 transition-all duration-300 hover:shadow-lg
      ${className}
    `}>
            {/* Header */}
            <div className="flex items-start space-x-4 mb-4">
                <div className={`
          flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center
          ${config.bgColor === 'bg-blue-50' ? 'bg-blue-100' :
                        config.bgColor === 'bg-yellow-50' ? 'bg-yellow-100' :
                            config.bgColor === 'bg-green-50' ? 'bg-green-100' :
                                config.bgColor === 'bg-red-50' ? 'bg-red-100' :
                                    config.bgColor === 'bg-purple-50' ? 'bg-purple-100' :
                                        'bg-indigo-100'}
        `}>
                    <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
                </div>
                <div className="flex-1">
                    <h3 className={`text-lg font-semibold ${config.titleColor} mb-2`}>
                        {title}
                    </h3>
                    {description && (
                        <p className={`${config.textColor} leading-relaxed`}>
                            {description}
                        </p>
                    )}
                </div>
            </div>

            {/* Content Items */}
            {items.length > 0 && (
                <div className="space-y-3">
                    {items.map((item, index) => (
                        <div key={index} className="flex items-start space-x-3">
                            <div className={`
                flex-shrink-0 w-2 h-2 rounded-full mt-2
                ${config.iconColor.replace('text-', 'bg-')}
              `}></div>
                            <div className="flex-1">
                                {typeof item === 'string' ? (
                                    <p className={`${config.textColor} leading-relaxed`}>
                                        {item}
                                    </p>
                                ) : (
                                    <div>
                                        {item.title && (
                                            <h4 className={`font-medium ${config.titleColor} mb-1`}>
                                                {item.title}
                                            </h4>
                                        )}
                                        <p className={`${config.textColor} leading-relaxed`}>
                                            {item.content}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

// Specialized instruction card components
export const TimeInstructionCard = ({ duration, questions, timePerQuestion }) => (
    <InstructionCard
        type="time"
        icon={Clock}
        title="Time Management"
        description="Manage your time effectively during the test."
        items={[
            `Total test duration: ${Math.floor(duration / 60)} minutes`,
            `Total questions: ${questions}`,
            `Average time per question: ${Math.floor(timePerQuestion / 60)}:${(timePerQuestion % 60).toString().padStart(2, '0')} minutes`,
            "A timer will be displayed throughout the test",
            "Test will auto-submit when time expires"
        ]}
    />
);

export const SecurityInstructionCard = () => (
    <InstructionCard
        type="security"
        icon={Shield}
        title="Security & Anti-Cheat Measures"
        description="This test employs strict security measures to ensure fair assessment."
        items={[
            "Test will run in fullscreen mode",
            "Tab switching is monitored and limited to 3 attempts",
            "Right-click and developer tools are disabled",
            "Screen recording and monitoring may be active",
            "Any violation may result in automatic test submission"
        ]}
        highlight={true}
    />
);

export const GeneralInstructionCard = ({ testTitle, totalQuestions, passingScore }) => (
    <InstructionCard
        type="info"
        icon={BookOpen}
        title="General Instructions"
        description={`Welcome to the ${testTitle}. Please read all instructions carefully.`}
        items={[
            `This test contains ${totalQuestions} questions`,
            `Passing score: ${passingScore}%`,
            "Read each question carefully before answering",
            "You can navigate between questions using the navigation panel",
            "Your answers are automatically saved",
            "Ensure stable internet connection throughout the test"
        ]}
    />
);

export const TechnicalInstructionCard = () => (
    <InstructionCard
        type="warning"
        icon={Eye}
        title="Technical Requirements"
        description="Ensure your system meets these requirements before starting."
        items={[
            {
                title: "Browser Compatibility",
                content: "Use latest version of Chrome, Firefox, Safari, or Edge"
            },
            {
                title: "Internet Connection",
                content: "Stable broadband connection required (minimum 1 Mbps)"
            },
            {
                title: "Screen Resolution",
                content: "Minimum 1024x768 resolution recommended"
            },
            {
                title: "Disable Extensions",
                content: "Disable browser extensions that might interfere with the test"
            },
            {
                title: "Close Other Applications",
                content: "Close unnecessary applications to ensure optimal performance"
            }
        ]}
    />
);

export const QuestionTypesCard = () => (
    <InstructionCard
        type="success"
        icon={Target}
        title="Question Types & Scoring"
        description="Understand different question formats and how they're scored."
        items={[
            {
                title: "Single Choice Questions",
                content: "Select only one correct answer from the given options"
            },
            {
                title: "Multiple Choice Questions",
                content: "Select all correct answers - partial credit may apply"
            },
            {
                title: "Code-based Questions",
                content: "Analyze code snippets and select the correct output or explanation"
            },
            {
                title: "Scoring System",
                content: "Each question has different point values based on difficulty level"
            }
        ]}
    />
);

export const ContactSupportCard = () => (
    <InstructionCard
        type="info"
        icon={Users}
        title="Need Help?"
        description="If you encounter any technical issues during the test."
        items={[
            "Contact the test administrator immediately",
            "Do not refresh or close the browser window",
            "Take a screenshot of any error messages",
            "Note down the exact time when the issue occurred",
            "Keep your test window open while seeking help"
        ]}
    />
);

export default InstructionCard;