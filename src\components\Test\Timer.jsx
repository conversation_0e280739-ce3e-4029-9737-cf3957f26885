import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

const Timer = ({
    initialTime,
    onTimeUp,
    isActive = true,
    showWarning = true,
    warningThreshold = 300 // 5 minutes in seconds
}) => {
    const [timeLeft, setTimeLeft] = useState(initialTime);
    const [isWarning, setIsWarning] = useState(false);

    useEffect(() => {
        setTimeLeft(initialTime);
    }, [initialTime]);

    useEffect(() => {
        if (!isActive) return;

        const timer = setInterval(() => {
            setTimeLeft((prevTime) => {
                if (prevTime <= 1) {
                    onTimeUp();
                    return 0;
                }

                const newTime = prevTime - 1;

                // Set warning state
                if (showWarning && newTime <= warningThreshold && !isWarning) {
                    setIsWarning(true);
                }

                return newTime;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);

    const formatTime = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const getTimerColor = () => {
        if (timeLeft <= 60) return 'text-red-600'; // Last minute
        if (isWarning) return 'text-orange-600'; // Warning threshold
        return 'text-gray-700'; // Normal
    };

    const getBackgroundColor = () => {
        if (timeLeft <= 60) return 'bg-red-50 border-red-200';
        if (isWarning) return 'bg-orange-50 border-orange-200';
        return 'bg-white border-gray-200';
    };

    return (
        <div className={`
      flex items-center space-x-2 px-4 py-2 rounded-lg border-2 
      ${getBackgroundColor()} transition-colors duration-300
    `}>
            <Clock className={`w-5 h-5 ${getTimerColor()}`} />
            <span className={`font-mono text-lg font-bold ${getTimerColor()}`}>
                {formatTime(timeLeft)}
            </span>
            {isWarning && timeLeft > 60 && (
                <span className="text-xs text-orange-600 font-medium">
                    Warning!
                </span>
            )}
            {timeLeft <= 60 && (
                <span className="text-xs text-red-600 font-medium animate-pulse">
                    Time Up Soon!
                </span>
            )}
        </div>
    );
};

export default Timer;