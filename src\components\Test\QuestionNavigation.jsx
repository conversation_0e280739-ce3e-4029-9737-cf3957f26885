import React from 'react';
import { CheckCircle, Circle, Clock } from 'lucide-react';

const QuestionNavigation = ({
    questions,
    currentQuestionIndex,
    answers,
    onQuestionSelect,
    isAnswered
}) => {
    const getAnsweredCount = () => {
        return Object.keys(answers).length;
    };

    const getQuestionStatus = (question, index) => {
        if (index === currentQuestionIndex) return 'current';
        if (isAnswered(question.id)) return 'answered';
        return 'unanswered';
    };

    const getStatusStyles = (status) => {
        switch (status) {
            case 'current':
                return 'bg-indigo-600 text-white shadow-lg ring-2 ring-indigo-300 scale-105';
            case 'answered':
                return 'bg-emerald-100 text-emerald-800 border-2 border-emerald-300 hover:bg-emerald-200';
            case 'unanswered':
                return 'bg-slate-100 text-slate-600 hover:bg-slate-200 border-2 border-slate-200';
            default:
                return 'bg-slate-100 text-slate-600';
        }
    };

    return (
        <div className="fixed left-6 top-1/2 transform -translate-y-1/2 z-30 hidden lg:block">
            <div className="bg-white rounded-2xl shadow-xl border border-slate-200 p-5 w-64">
                {/* Header */}
                <div className="mb-4">
                    <h3 className="font-bold text-slate-900 text-lg mb-2">Questions</h3>
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-600">Progress</span>
                        <span className="font-semibold text-indigo-600">
                            {getAnsweredCount()}/{questions.length}
                        </span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
                        <div 
                            className="bg-gradient-to-r from-indigo-500 to-emerald-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(getAnsweredCount() / questions.length) * 100}%` }}
                        ></div>
                    </div>
                </div>

                {/* Question Grid */}
                <div className="grid grid-cols-4 gap-2 mb-4">
                    {questions.map((question, index) => {
                        const status = getQuestionStatus(question, index);
                        return (
                            <button
                                key={question.id}
                                onClick={() => onQuestionSelect(index)}
                                className={`
                                    w-12 h-12 rounded-xl text-sm font-bold transition-all duration-200 
                                    flex items-center justify-center relative
                                    ${getStatusStyles(status)}
                                `}
                                title={`Question ${index + 1} - ${question.category}`}
                            >
                                <span className="relative z-10">{index + 1}</span>
                                {status === 'answered' && (
                                    <CheckCircle className="absolute top-0 right-0 w-4 h-4 text-emerald-600 transform translate-x-1 -translate-y-1" />
                                )}
                                {status === 'current' && (
                                    <div className="absolute inset-0 rounded-xl bg-indigo-400 opacity-20 animate-pulse"></div>
                                )}
                            </button>
                        );
                    })}
                </div>

                {/* Legend */}
                <div className="space-y-2 text-xs border-t border-slate-200 pt-3">
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-indigo-600 rounded-full"></div>
                        <span className="text-slate-600">Current</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-emerald-100 border-2 border-emerald-300 rounded-full"></div>
                        <span className="text-slate-600">Answered</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-slate-100 border-2 border-slate-200 rounded-full"></div>
                        <span className="text-slate-600">Not answered</span>
                    </div>
                </div>

                {/* Current Question Info */}
                <div className="mt-4 pt-3 border-t border-slate-200">
                    <div className="text-xs text-slate-600 mb-2">Current Question</div>
                    <div className="text-sm font-medium text-slate-900 mb-1">
                        {questions[currentQuestionIndex]?.category || 'General'}
                    </div>
                    <div className="text-xs text-slate-500">
                        Question {currentQuestionIndex + 1} of {questions.length}
                    </div>
                </div>

                {/* Quick Stats */}
                <div className="mt-4 pt-3 border-t border-slate-200">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="text-center">
                            <div className="font-semibold text-slate-900">{getAnsweredCount()}</div>
                            <div className="text-slate-500">Completed</div>
                        </div>
                        <div className="text-center">
                            <div className="font-semibold text-slate-900">{questions.length - getAnsweredCount()}</div>
                            <div className="text-slate-500">Remaining</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default QuestionNavigation;
