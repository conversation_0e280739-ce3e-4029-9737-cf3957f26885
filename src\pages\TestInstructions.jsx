import React, { useState, useEffect } from 'react';
import {
    Play,
    CheckSquare,
    AlertCircle,
    Download,
    FileText,
    Printer,
    Share2,
    ArrowRight,
    Clock,
    Shield,
    BookOpen,
    User,
    Calendar
} from 'lucide-react';
import InstructionCard, {
    TimeInstructionCard,
    SecurityInstructionCard,
    GeneralInstructionCard,
    TechnicalInstructionCard,
    QuestionTypesCard,
    ContactSupportCard
} from '../components/Test/InstructionCard';
import Button from '../components/UI/Button';
import Modal, { ConfirmModal } from '../components/UI/Modal';

// Dummy test data for instructions
const testInstructionsData = {
    testId: 'test_001',
    title: 'Software Developer Aptitude Test',
    description: 'A comprehensive assessment covering programming concepts, logical reasoning, problem-solving skills, and technical knowledge required for software development roles.',
    duration: 3600, // 60 minutes
    totalQuestions: 15,
    passingScore: 70,
    timePerQuestion: 240, // 4 minutes average
    testDate: '2024-07-28',
    testTime: '10:00 AM',
    candidate: {
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        id: 'CAND_001'
    },
    organization: 'TechCorp Solutions',
    instructions: {
        general: [
            'This is a timed assessment that evaluates your programming knowledge and problem-solving abilities.',
            'Answer all questions to the best of your ability.',
            'Questions may include code analysis, algorithmic thinking, and theoretical concepts.',
            'Some questions may have partial scoring for multiple-choice answers.'
        ],
        specific: [
            'Carefully read each question before selecting your answer.',
            'For multiple-choice questions, select ALL correct answers.',
            'Code-based questions may require you to predict output or identify errors.',
            'Use the question navigation panel to move between questions.',
            'You can change your answers until you submit the test.'
        ],
        restrictions: [
            'No external resources, books, or notes are allowed.',
            'Calculator use is permitted for mathematical calculations.',
            'Communication with others during the test is strictly prohibited.',
            'Screen recording or photography of test content is not allowed.'
        ]
    },
    systemRequirements: {
        browser: 'Chrome 90+, Firefox 88+, Safari 14+, Edge 90+',
        internet: 'Stable broadband connection (minimum 1 Mbps)',
        screen: 'Minimum 1024x768 resolution',
        os: 'Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+'
    }
};

const TestInstructions = ({ onStartTest = () => { } }) => {
    const [checkedItems, setCheckedItems] = useState({});
    const [allChecked, setAllChecked] = useState(false);
    const [showStartModal, setShowStartModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showPrintModal, setShowPrintModal] = useState(false);
    const [currentTime, setCurrentTime] = useState(new Date());

    const checklistItems = [
        { id: 'read_instructions', text: 'I have read and understood all test instructions' },
        { id: 'technical_requirements', text: 'My system meets all technical requirements' },
        { id: 'stable_internet', text: 'I have a stable internet connection' },
        { id: 'quiet_environment', text: 'I am in a quiet, distraction-free environment' },
        { id: 'no_external_help', text: 'I understand that external help is not allowed' },
        { id: 'time_commitment', text: 'I have enough time to complete the full test' },
        { id: 'honest_attempt', text: 'I will attempt the test honestly and to the best of my ability' }
    ];

    // Update current time every minute
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 60000);

        return () => clearInterval(timer);
    }, []);

    // Check if all items are checked
    useEffect(() => {
        const allItemsChecked = checklistItems.every(item => checkedItems[item.id]);
        setAllChecked(allItemsChecked);
    }, [checkedItems]);

    const handleCheckItem = (itemId) => {
        setCheckedItems(prev => ({
            ...prev,
            [itemId]: !prev[itemId]
        }));
    };

    const handleStartTest = async () => {
        setIsLoading(true);

        try {
            // Simulate preparation time
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Log test start
            console.log('Starting test:', {
                testId: testInstructionsData.testId,
                candidateId: testInstructionsData.candidate.id,
                startTime: new Date().toISOString(),
                checklist: checkedItems
            });

            // Call the parent function to navigate to test
            onStartTest();

        } catch (error) {
            console.error('Failed to start test:', error);
        } finally {
            setIsLoading(false);
            setShowStartModal(false);
        }
    };

    const handlePrintInstructions = () => {
        window.print();
        setShowPrintModal(false);
    };

    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            {/* Header */}
            <div className="bg-white shadow-sm border-b border-gray-200">
                <div className="max-w-6xl mx-auto px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                                <FileText className="w-6 h-6 text-white" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">Test Instructions</h1>
                                <p className="text-gray-600">{testInstructionsData.organization}</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            <div className="text-right text-sm text-gray-600">
                                <div className="flex items-center space-x-2">
                                    <Clock className="w-4 h-4" />
                                    <span>Current Time: {formatTime(currentTime)}</span>
                                </div>
                                <div className="flex items-center space-x-2 mt-1">
                                    <Calendar className="w-4 h-4" />
                                    <span>{formatDate(testInstructionsData.testDate)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Main Content */}
            <div className="max-w-6xl mx-auto px-6 py-8">
                {/* Test Overview */}
                <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900 mb-4">
                                {testInstructionsData.title}
                            </h2>
                            <p className="text-gray-600 leading-relaxed mb-6">
                                {testInstructionsData.description}
                            </p>

                            {/* Candidate Info */}
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                                    <User className="w-5 h-5 mr-2" />
                                    Candidate Information
                                </h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Name:</span>
                                        <span className="font-medium">{testInstructionsData.candidate.name}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Email:</span>
                                        <span className="font-medium">{testInstructionsData.candidate.email}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Candidate ID:</span>
                                        <span className="font-medium">{testInstructionsData.candidate.id}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Test Stats */}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="bg-blue-50 rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold text-blue-600 mb-1">
                                    {Math.floor(testInstructionsData.duration / 60)}
                                </div>
                                <div className="text-sm text-blue-800">Minutes</div>
                            </div>
                            <div className="bg-green-50 rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold text-green-600 mb-1">
                                    {testInstructionsData.totalQuestions}
                                </div>
                                <div className="text-sm text-green-800">Questions</div>
                            </div>
                            <div className="bg-purple-50 rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold text-purple-600 mb-1">
                                    {testInstructionsData.passingScore}%
                                </div>
                                <div className="text-sm text-purple-800">Pass Score</div>
                            </div>
                            <div className="bg-orange-50 rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold text-orange-600 mb-1">
                                    {Math.floor(testInstructionsData.timePerQuestion / 60)}
                                </div>
                                <div className="text-sm text-orange-800">Min/Question</div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Instruction Cards Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <GeneralInstructionCard
                        testTitle={testInstructionsData.title}
                        totalQuestions={testInstructionsData.totalQuestions}
                        passingScore={testInstructionsData.passingScore}
                    />
                    <TimeInstructionCard
                        duration={testInstructionsData.duration}
                        questions={testInstructionsData.totalQuestions}
                        timePerQuestion={testInstructionsData.timePerQuestion}
                    />
                    <TechnicalInstructionCard />
                    <QuestionTypesCard />
                    <div className="lg:col-span-2">
                        <SecurityInstructionCard />
                    </div>
                    <ContactSupportCard />
                    {/* Custom Instructions */}
                    <InstructionCard
                        type="warning"
                        icon={AlertCircle}
                        title="Important Restrictions"
                        description="Please note these important restrictions during the test."
                        items={testInstructionsData.instructions.restrictions}
                    />
                </div>
                {/* Pre-Test Checklist */}
                <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <CheckSquare className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900">Pre-Test Checklist</h3>
                            <p className="text-gray-600">Please confirm all items below before starting the test</p>
                        </div>
                    </div>

                    <div className="space-y-4">
                        {checklistItems.map((item) => (
                            <div
                                key={item.id}
                                className={`
                  flex items-center space-x-4 p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                  ${checkedItems[item.id]
                                        ? 'border-green-200 bg-green-50'
                                        : 'border-gray-200 bg-white hover:border-gray-300'
                                    }
                `}
                                onClick={() => handleCheckItem(item.id)}
                            >
                                <div className={`
                  w-6 h-6 rounded border-2 flex items-center justify-center cursor-pointer
                  ${checkedItems[item.id]
                                        ? 'border-green-500 bg-green-500'
                                        : 'border-gray-300 bg-white'
                                    }
                `}>
                                    {checkedItems[item.id] && (
                                        <CheckSquare className="w-4 h-4 text-white" />
                                    )}
                                </div>
                                <span className={`
                  ${checkedItems[item.id] ? 'text-green-800' : 'text-gray-700'}
                  font-medium
                `}>
                                    {item.text}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Start Test Section */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg p-8 text-white text-center">
                    <div className="mb-6">
                        <h3 className="text-2xl font-bold mb-2">Ready to Begin?</h3>
                        <p className="text-blue-100">
                            Once you start the test, the timer will begin and you cannot pause or restart.
                        </p>
                    </div>

                    <div className="flex items-center justify-center space-x-4">
                        <Button
                            variant="secondary"
                            size="lg"
                            onClick={() => window.location.reload()}
                        >
                            Review Instructions
                        </Button>

                        <Button
                            variant="success"
                            size="lg"
                            icon={Play}
                            onClick={() => setShowStartModal(true)}
                            disabled={!allChecked}
                            className={`
                ${allChecked
                                    ? 'shadow-lg hover:shadow-xl'
                                    : 'opacity-50 cursor-not-allowed'
                                }
              `}
                        >
                            Start Test
                        </Button>
                    </div>

                    {!allChecked && (
                        <p className="text-blue-200 text-sm mt-4">
                            Please complete the pre-test checklist to continue
                        </p>
                    )}
                </div>
            </div>

            {/* Modals */}
            <ConfirmModal
                isOpen={showStartModal}
                onClose={() => setShowStartModal(false)}
                onConfirm={handleStartTest}
                title="Start Test Confirmation"
                message="Are you ready to begin the test? Once started, the timer will run and you cannot pause or restart the test."
                confirmText={isLoading ? "Starting..." : "Yes, Start Test"}
                cancelText="Cancel"
                type="info"
            />

            <Modal
                isOpen={showPrintModal}
                onClose={() => setShowPrintModal(false)}
                title="Print Instructions"
                footer={
                    <div className="flex space-x-3">
                        <Button variant="secondary" onClick={() => setShowPrintModal(false)}>
                            Cancel
                        </Button>
                        <Button variant="primary" icon={Printer} onClick={handlePrintInstructions}>
                            Print
                        </Button>
                    </div>
                }
            >
                <p className="text-gray-600 mb-4">
                    This will print the test instructions for your reference. Make sure your printer is ready.
                </p>
                <div className="bg-yellow-50 p-4 rounded-lg">
                    <p className="text-sm text-yellow-800">
                        <strong>Note:</strong> Printed instructions are for reference only. The actual test must be taken online.
                    </p>
                </div>
            </Modal>
        </div>
    );
};

export default TestInstructions;